# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Cache
cache/
.cache/
*.cache

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Backup files
*.bak
*.backup
*_backup*
*_old*

# OS
.DS_Store
Thumbs.db

# Model files (large)
*.pkl
*.joblib
*.h5
*.pt
*.pth

# Data files (if large)
# data/
# *.csv
# *.json

# MLflow
mlruns/
mlflow.db

# Jupyter
.ipynb_checkpoints/
*.ipynb

# Coverage
.coverage
htmlcov/

# Testing
.pytest_cache/
.tox/