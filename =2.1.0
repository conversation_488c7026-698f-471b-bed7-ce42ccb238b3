Requirement already satisfied: torch in ./.venv/lib/python3.10/site-packages (1.13.1+cpu)
Collecting torch
  Using cached torch-2.7.1-cp310-cp310-manylinux_2_28_x86_64.whl.metadata (29 kB)
Requirement already satisfied: filelock in ./.venv/lib/python3.10/site-packages (from torch) (3.18.0)
Requirement already satisfied: typing-extensions>=4.10.0 in ./.venv/lib/python3.10/site-packages (from torch) (4.13.2)
Requirement already satisfied: sympy>=1.13.3 in ./.venv/lib/python3.10/site-packages (from torch) (1.14.0)
Requirement already satisfied: networkx in ./.venv/lib/python3.10/site-packages (from torch) (3.4.2)
Requirement already satisfied: jinja2 in ./.venv/lib/python3.10/site-packages (from torch) (3.1.6)
Requirement already satisfied: fsspec in ./.venv/lib/python3.10/site-packages (from torch) (2025.5.1)
Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.6.77 in ./.venv/lib/python3.10/site-packages (from torch) (12.6.77)
Requirement already satisfied: nvidia-cuda-runtime-cu12==12.6.77 in ./.venv/lib/python3.10/site-packages (from torch) (12.6.77)
Requirement already satisfied: nvidia-cuda-cupti-cu12==12.6.80 in ./.venv/lib/python3.10/site-packages (from torch) (12.6.80)
Collecting nvidia-cudnn-cu12==9.5.1.17 (from torch)
  Using cached nvidia_cudnn_cu12-9.5.1.17-py3-none-manylinux_2_28_x86_64.whl.metadata (1.6 kB)
Collecting nvidia-cublas-cu12==12.6.4.1 (from torch)
  Using cached nvidia_cublas_cu12-12.6.4.1-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)
Requirement already satisfied: nvidia-cufft-cu12==11.3.0.4 in ./.venv/lib/python3.10/site-packages (from torch) (11.3.0.4)
Requirement already satisfied: nvidia-curand-cu12==10.3.7.77 in ./.venv/lib/python3.10/site-packages (from torch) (10.3.7.77)
Collecting nvidia-cusolver-cu12==11.7.1.2 (from torch)
  Using cached nvidia_cusolver_cu12-11.7.1.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.6 kB)
Requirement already satisfied: nvidia-cusparse-cu12==12.5.4.2 in ./.venv/lib/python3.10/site-packages (from torch) (12.5.4.2)
Requirement already satisfied: nvidia-cusparselt-cu12==0.6.3 in ./.venv/lib/python3.10/site-packages (from torch) (0.6.3)
Requirement already satisfied: nvidia-nccl-cu12==2.26.2 in ./.venv/lib/python3.10/site-packages (from torch) (2.26.2)
Requirement already satisfied: nvidia-nvtx-cu12==12.6.77 in ./.venv/lib/python3.10/site-packages (from torch) (12.6.77)
Requirement already satisfied: nvidia-nvjitlink-cu12==12.6.85 in ./.venv/lib/python3.10/site-packages (from torch) (12.6.85)
Requirement already satisfied: nvidia-cufile-cu12==1.11.1.6 in ./.venv/lib/python3.10/site-packages (from torch) (1.11.1.6)
Requirement already satisfied: triton==3.3.1 in ./.venv/lib/python3.10/site-packages (from torch) (3.3.1)
Requirement already satisfied: setuptools>=40.8.0 in ./.venv/lib/python3.10/site-packages (from triton==3.3.1->torch) (78.1.0)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in ./.venv/lib/python3.10/site-packages (from sympy>=1.13.3->torch) (1.3.0)
Requirement already satisfied: MarkupSafe>=2.0 in ./.venv/lib/python3.10/site-packages (from jinja2->torch) (3.0.2)
Using cached torch-2.7.1-cp310-cp310-manylinux_2_28_x86_64.whl (821.2 MB)
Downloading nvidia_cublas_cu12-12.6.4.1-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (393.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 393.1/393.1 MB 623.4 kB/s eta 0:00:00
Downloading nvidia_cudnn_cu12-9.5.1.17-py3-none-manylinux_2_28_x86_64.whl (571.0 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 571.0/571.0 MB 520.5 kB/s eta 0:00:00
Downloading nvidia_cusolver_cu12-11.7.1.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (158.2 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 158.2/158.2 MB 821.9 kB/s eta 0:00:00
Installing collected packages: nvidia-cublas-cu12, nvidia-cusolver-cu12, nvidia-cudnn-cu12, torch
  Attempting uninstall: torch
    Found existing installation: torch 1.13.1+cpu
    Can't uninstall 'torch'. No files were found to uninstall.
Successfully installed nvidia-cublas-cu12-12.6.4.1 nvidia-cudnn-cu12-9.5.1.17 nvidia-cusolver-cu12-11.7.1.2 torch-2.7.1
1+cpu:
      Successfully uninstalled torch-1.13.1+cpu
Successfully installed nvidia-cublas-cu12-12.6.4.1 nvidia-cudnn-cu12-9.5.1.17 nvidia-cusolver-cu12-11.7.1.2 torch-2.7.1
