#!/usr/bin/env python3
"""
Fixed evaluation test that properly aligns user IDs with ground truth.
This addresses the 0.000 metrics issue by ensuring data consistency.
"""

import os
import sys
import pandas as pd
import numpy as np
import time
import json
from datetime import datetime
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')

def load_aligned_dataset():
    """Load dataset with proper alignment between profiles and ground truth."""
    print("📊 Loading and Aligning Dataset...")
    
    # Load all data
    ground_truth = pd.read_csv("test_dataset/merged_ground_truth.csv")
    linkedin_profiles = pd.read_csv("test_dataset/merged_linkedin_profiles.csv")
    instagram_profiles = pd.read_csv("test_dataset/merged_instagram_profiles.csv")
    
    print(f"✅ Ground Truth: {len(ground_truth)} pairs")
    print(f"✅ LinkedIn Profiles: {len(linkedin_profiles)} users")
    print(f"✅ Instagram Profiles: {len(instagram_profiles)} users")
    
    # Get the user IDs that exist in ground truth
    gt_linkedin_ids = set(ground_truth['linkedin_id'].unique())
    gt_instagram_ids = set(ground_truth['instagram_id'].unique())
    
    # Filter profiles to only include users in ground truth
    linkedin_filtered = linkedin_profiles[linkedin_profiles['user_id'].isin(gt_linkedin_ids)]
    instagram_filtered = instagram_profiles[instagram_profiles['user_id'].isin(gt_instagram_ids)]
    
    print(f"✅ Filtered LinkedIn: {len(linkedin_filtered)} users (in ground truth)")
    print(f"✅ Filtered Instagram: {len(instagram_filtered)} users (in ground truth)")
    
    # Take a subset for testing (first 100 pairs)
    test_ground_truth = ground_truth.head(100)
    test_linkedin_ids = set(test_ground_truth['linkedin_id'].unique())
    test_instagram_ids = set(test_ground_truth['instagram_id'].unique())
    
    test_linkedin = linkedin_filtered[linkedin_filtered['user_id'].isin(test_linkedin_ids)]
    test_instagram = instagram_filtered[instagram_filtered['user_id'].isin(test_instagram_ids)]
    
    print(f"✅ Test LinkedIn: {len(test_linkedin)} users")
    print(f"✅ Test Instagram: {len(test_instagram)} users")
    print(f"✅ Test Ground Truth: {len(test_ground_truth)} pairs")
    
    return test_linkedin, test_instagram, test_ground_truth

def generate_embeddings_for_users(profiles, platform_name):
    """Generate BERT embeddings for specific users."""
    try:
        from features.semantic_embedder import SemanticEmbedder
        
        embedder = SemanticEmbedder(
            model_name='sentence-transformers/all-MiniLM-L6-v2',
            use_sentence_transformer=True
        )
        
        # Create content from profiles
        test_data = []
        for _, profile in profiles.iterrows():
            content = f"{profile['name']} works as {profile['field_of_interest']} in {profile['location']}"
            test_data.append({
                'user_id': profile['user_id'],
                'content': content
            })
        
        test_df = pd.DataFrame(test_data)
        
        print(f"  Generating embeddings for {len(test_df)} {platform_name} users...")
        
        embeddings = embedder.fit_transform(
            data=test_df,
            platform_name=platform_name,
            text_col='content',
            user_id_col='user_id',
            batch_size=16
        )
        
        print(f"  ✅ Generated {len(embeddings)} embeddings")
        return embeddings
        
    except Exception as e:
        print(f"  ❌ Failed to generate embeddings: {e}")
        return {}

def perform_matching_with_ground_truth(linkedin_embeddings, instagram_embeddings, ground_truth):
    """Perform matching and evaluate against ground truth properly."""
    from sklearn.metrics.pairwise import cosine_similarity
    
    print("🎯 Performing User Matching...")
    
    # Get all possible pairs from ground truth
    all_matches = []
    
    for _, row in ground_truth.iterrows():
        linkedin_id = row['linkedin_id']
        instagram_id = row['instagram_id']
        is_same_user = row['is_same_user']
        
        # Check if we have embeddings for both users
        if linkedin_id in linkedin_embeddings and instagram_id in instagram_embeddings:
            # Calculate similarity
            linkedin_emb = linkedin_embeddings[linkedin_id]
            instagram_emb = instagram_embeddings[instagram_id]
            
            similarity = np.dot(linkedin_emb, instagram_emb) / (
                np.linalg.norm(linkedin_emb) * np.linalg.norm(instagram_emb)
            )
            
            all_matches.append({
                'linkedin_id': linkedin_id,
                'instagram_id': instagram_id,
                'similarity': similarity,
                'true_label': is_same_user,
                'predicted_label': 1 if similarity > 0.5 else 0  # Simple threshold
            })
    
    print(f"✅ Evaluated {len(all_matches)} user pairs")
    return all_matches

def calculate_proper_metrics(matches):
    """Calculate metrics properly from the matches."""
    if not matches:
        return {
            'accuracy': 0.0,
            'precision': 0.0,
            'recall': 0.0,
            'f1_score': 0.0,
            'total_pairs': 0,
            'error': 'No matches to evaluate'
        }
    
    # Extract true and predicted labels
    true_labels = [match['true_label'] for match in matches]
    predicted_labels = [match['predicted_label'] for match in matches]
    
    # Calculate confusion matrix components
    tp = sum(1 for t, p in zip(true_labels, predicted_labels) if t == 1 and p == 1)
    fp = sum(1 for t, p in zip(true_labels, predicted_labels) if t == 0 and p == 1)
    tn = sum(1 for t, p in zip(true_labels, predicted_labels) if t == 0 and p == 0)
    fn = sum(1 for t, p in zip(true_labels, predicted_labels) if t == 1 and p == 0)
    
    # Calculate metrics
    total = len(matches)
    accuracy = (tp + tn) / total if total > 0 else 0
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    # Calculate additional metrics
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'specificity': specificity,
        'confusion_matrix': {
            'tp': tp, 'fp': fp, 'tn': tn, 'fn': fn
        },
        'total_pairs': total,
        'positive_pairs': sum(true_labels),
        'predicted_positive': sum(predicted_labels)
    }

def analyze_similarity_distribution(matches):
    """Analyze the distribution of similarity scores."""
    if not matches:
        return {}
    
    similarities = [match['similarity'] for match in matches]
    true_positives = [match['similarity'] for match in matches if match['true_label'] == 1]
    true_negatives = [match['similarity'] for match in matches if match['true_label'] == 0]
    
    return {
        'all_similarities': {
            'mean': np.mean(similarities),
            'std': np.std(similarities),
            'min': np.min(similarities),
            'max': np.max(similarities)
        },
        'true_positive_similarities': {
            'mean': np.mean(true_positives) if true_positives else 0,
            'std': np.std(true_positives) if true_positives else 0,
            'count': len(true_positives)
        },
        'true_negative_similarities': {
            'mean': np.mean(true_negatives) if true_negatives else 0,
            'std': np.std(true_negatives) if true_negatives else 0,
            'count': len(true_negatives)
        }
    }

def test_with_different_thresholds(matches):
    """Test performance with different similarity thresholds."""
    if not matches:
        return {}
    
    thresholds = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
    results = {}
    
    for threshold in thresholds:
        # Apply threshold
        for match in matches:
            match['predicted_label'] = 1 if match['similarity'] > threshold else 0
        
        # Calculate metrics
        metrics = calculate_proper_metrics(matches)
        results[threshold] = {
            'f1_score': metrics['f1_score'],
            'precision': metrics['precision'],
            'recall': metrics['recall'],
            'accuracy': metrics['accuracy']
        }
    
    return results

def main():
    """Main test function with proper evaluation."""
    print("🔧 FIXED CROSSEMBEDUID EVALUATION TEST")
    print("=" * 60)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    start_time = time.time()
    
    # Load aligned dataset
    linkedin_profiles, instagram_profiles, ground_truth = load_aligned_dataset()
    
    # Generate embeddings for the specific users in ground truth
    print("\n🧠 Generating BERT Embeddings...")
    linkedin_embeddings = generate_embeddings_for_users(linkedin_profiles, 'linkedin')
    instagram_embeddings = generate_embeddings_for_users(instagram_profiles, 'instagram')
    
    if not linkedin_embeddings or not instagram_embeddings:
        print("❌ Failed to generate embeddings")
        return
    
    # Perform matching with proper ground truth alignment
    matches = perform_matching_with_ground_truth(
        linkedin_embeddings, instagram_embeddings, ground_truth
    )
    
    # Calculate proper metrics
    print("\n📊 Calculating Metrics...")
    metrics = calculate_proper_metrics(matches)
    
    # Analyze similarity distribution
    similarity_analysis = analyze_similarity_distribution(matches)
    
    # Test different thresholds
    threshold_analysis = test_with_different_thresholds(matches)
    
    total_time = time.time() - start_time
    
    # Display results
    print("\n" + "=" * 60)
    print("📋 FIXED EVALUATION RESULTS")
    print("=" * 60)
    
    print(f"🎯 PERFORMANCE METRICS:")
    print(f"   Accuracy:     {metrics['accuracy']:.3f}")
    print(f"   Precision:    {metrics['precision']:.3f}")
    print(f"   Recall:       {metrics['recall']:.3f}")
    print(f"   F1-Score:     {metrics['f1_score']:.3f}")
    print(f"   Specificity:  {metrics['specificity']:.3f}")
    
    print(f"\n📈 CONFUSION MATRIX:")
    cm = metrics['confusion_matrix']
    print(f"   True Positives:  {cm['tp']}")
    print(f"   False Positives: {cm['fp']}")
    print(f"   True Negatives:  {cm['tn']}")
    print(f"   False Negatives: {cm['fn']}")
    
    print(f"\n📊 DATA SUMMARY:")
    print(f"   Total Pairs Evaluated: {metrics['total_pairs']}")
    print(f"   Positive Pairs (Ground Truth): {metrics['positive_pairs']}")
    print(f"   Predicted Positive: {metrics['predicted_positive']}")
    
    print(f"\n🎯 SIMILARITY ANALYSIS:")
    if similarity_analysis:
        print(f"   Overall Mean Similarity: {similarity_analysis['all_similarities']['mean']:.3f}")
        print(f"   True Matches Mean: {similarity_analysis['true_positive_similarities']['mean']:.3f}")
        print(f"   Non-Matches Mean: {similarity_analysis['true_negative_similarities']['mean']:.3f}")
    
    print(f"\n⚡ PERFORMANCE:")
    print(f"   Total Processing Time: {total_time:.2f} seconds")
    print(f"   LinkedIn Embeddings: {len(linkedin_embeddings)}")
    print(f"   Instagram Embeddings: {len(instagram_embeddings)}")
    
    # Find best threshold
    if threshold_analysis:
        best_threshold = max(threshold_analysis.keys(), 
                           key=lambda t: threshold_analysis[t]['f1_score'])
        print(f"\n🎯 BEST THRESHOLD ANALYSIS:")
        print(f"   Best Threshold: {best_threshold}")
        print(f"   Best F1-Score: {threshold_analysis[best_threshold]['f1_score']:.3f}")
        print(f"   Best Precision: {threshold_analysis[best_threshold]['precision']:.3f}")
        print(f"   Best Recall: {threshold_analysis[best_threshold]['recall']:.3f}")
    
    # Save results
    results = {
        'metrics': metrics,
        'similarity_analysis': similarity_analysis,
        'threshold_analysis': threshold_analysis,
        'processing_time': total_time,
        'dataset_info': {
            'linkedin_users': len(linkedin_embeddings),
            'instagram_users': len(instagram_embeddings),
            'total_pairs_evaluated': len(matches)
        },
        'test_timestamp': datetime.now().isoformat()
    }
    
    os.makedirs("fixed_test_results", exist_ok=True)
    with open("fixed_test_results/proper_evaluation.json", 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📁 Results saved to: fixed_test_results/")
    print("=" * 60)
    
    return results

if __name__ == "__main__":
    main()
