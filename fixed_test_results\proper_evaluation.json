{"metrics": {"accuracy": 0.0, "precision": 0, "recall": 0.0, "f1_score": 0, "specificity": 0, "confusion_matrix": {"tp": 0, "fp": 0, "tn": 0, "fn": 100}, "total_pairs": 100, "positive_pairs": 100, "predicted_positive": 0}, "similarity_analysis": {"all_similarities": {"mean": "0.0026652662", "std": "0.10246704", "min": "-0.22933516", "max": "0.3213979"}, "true_positive_similarities": {"mean": "0.0026652662", "std": "0.10246704", "count": 100}, "true_negative_similarities": {"mean": 0, "std": 0, "count": 0}}, "threshold_analysis": {"0.1": {"f1_score": 0.2758620689655173, "precision": 1.0, "recall": 0.16, "accuracy": 0.16}, "0.2": {"f1_score": 0.058252427184466014, "precision": 1.0, "recall": 0.03, "accuracy": 0.03}, "0.3": {"f1_score": 0.019801980198019802, "precision": 1.0, "recall": 0.01, "accuracy": 0.01}, "0.4": {"f1_score": 0, "precision": 0, "recall": 0.0, "accuracy": 0.0}, "0.5": {"f1_score": 0, "precision": 0, "recall": 0.0, "accuracy": 0.0}, "0.6": {"f1_score": 0, "precision": 0, "recall": 0.0, "accuracy": 0.0}, "0.7": {"f1_score": 0, "precision": 0, "recall": 0.0, "accuracy": 0.0}, "0.8": {"f1_score": 0, "precision": 0, "recall": 0.0, "accuracy": 0.0}, "0.9": {"f1_score": 0, "precision": 0, "recall": 0.0, "accuracy": 0.0}}, "processing_time": 21.764832735061646, "dataset_info": {"linkedin_users": 100, "instagram_users": 100, "total_pairs_evaluated": 100}, "test_timestamp": "2025-06-21T15:27:59.973151"}