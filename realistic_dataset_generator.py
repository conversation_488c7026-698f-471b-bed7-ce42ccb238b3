#!/usr/bin/env python3
"""
Realistic Cross-Platform User Identification Dataset Generator

This script generates a comprehensive, challenging dataset for testing the 
cross-platform user identification system with realistic LinkedIn and Instagram profiles.
"""

import pandas as pd
import numpy as np
import networkx as nx
import random
import json
import os
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Tuple, Optional
import uuid

# Set random seeds for reproducibility
random.seed(42)
np.random.seed(42)

class RealisticDatasetGenerator:
    """Generate realistic cross-platform user identification datasets."""
    
    def __init__(self):
        """Initialize the dataset generator with realistic data pools."""
        
        # Realistic first names (diverse, international)
        self.first_names = [
            "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>in", "<PERSON>", "<PERSON>"
        ]
        
        # <PERSON><PERSON> last names (diverse, international)
        self.last_names = [
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Patel", "Rodriguez", "Smith", "Taylor",
            "Thompson", "Wang", "Williams", "Wilson", "Ahmed", "Ali", "Gonzalez", "Hernandez",
            "Lopez", "Perez", "Sanchez", "Singh", "Kumar", "Sharma", "Gupta", "Jain",
            "Shah", "Mehta", "Agarwal", "Verma", "Mishra", "Tiwari", "Yadav", "Pandey",
            "Rossi", "Russo", "Ferrari", "Esposito", "Bianchi", "Romano", "Colombo", "Ricci",
            "Marino", "Greco", "Bruno", "Gallo", "Conti", "De Luca", "Mancini", "Costa"
        ]
        
        # Professional industries and job titles
        self.industries = {
            "Technology": [
                "Software Engineer", "Data Scientist", "Product Manager", "UX Designer", 
                "DevOps Engineer", "Machine Learning Engineer", "Frontend Developer", 
                "Backend Developer", "Full Stack Developer", "Technical Lead", "CTO", 
                "Engineering Manager", "Solutions Architect", "Cloud Engineer", "AI Researcher"
            ],
            "Finance": [
                "Financial Analyst", "Investment Banker", "Portfolio Manager", "Risk Analyst",
                "Quantitative Analyst", "Financial Advisor", "Compliance Officer", "CFO",
                "Accounting Manager", "Credit Analyst", "Treasury Analyst", "Auditor"
            ],
            "Healthcare": [
                "Physician", "Nurse", "Pharmacist", "Medical Researcher", "Healthcare Administrator",
                "Physical Therapist", "Radiologist", "Surgeon", "Pediatrician", "Cardiologist",
                "Psychiatrist", "Medical Device Engineer", "Clinical Data Manager"
            ],
            "Marketing": [
                "Digital Marketing Manager", "Content Marketing Specialist", "SEO Specialist",
                "Social Media Manager", "Brand Manager", "Marketing Analyst", "Growth Hacker",
                "Email Marketing Specialist", "PPC Specialist", "Marketing Director", "CMO"
            ],
            "Education": [
                "Teacher", "Professor", "Principal", "Education Administrator", "Curriculum Developer",
                "Academic Researcher", "School Counselor", "Librarian", "Education Technology Specialist"
            ],
            "Consulting": [
                "Management Consultant", "Strategy Consultant", "Business Analyst", "Operations Consultant",
                "IT Consultant", "HR Consultant", "Financial Consultant", "Marketing Consultant"
            ]
        }
        
        # Companies by industry
        self.companies = {
            "Technology": [
                "Google", "Microsoft", "Apple", "Amazon", "Meta", "Netflix", "Tesla", "Uber",
                "Airbnb", "Spotify", "Adobe", "Salesforce", "Oracle", "IBM", "Intel", "NVIDIA",
                "Zoom", "Slack", "Dropbox", "GitHub", "Atlassian", "Shopify", "Square", "Stripe"
            ],
            "Finance": [
                "Goldman Sachs", "JPMorgan Chase", "Bank of America", "Wells Fargo", "Citigroup",
                "Morgan Stanley", "BlackRock", "Vanguard", "Fidelity", "Charles Schwab", "American Express"
            ],
            "Healthcare": [
                "Johnson & Johnson", "Pfizer", "Roche", "Novartis", "Merck", "AbbVie", "Bristol Myers Squibb",
                "Eli Lilly", "Amgen", "Gilead Sciences", "Mayo Clinic", "Cleveland Clinic", "Kaiser Permanente"
            ],
            "Marketing": [
                "WPP", "Omnicom", "Publicis", "Interpublic", "Dentsu", "Havas", "Ogilvy", "BBDO",
                "DDB", "McCann", "Grey", "Leo Burnett", "Saatchi & Saatchi", "TBWA", "R/GA"
            ],
            "Education": [
                "Harvard University", "Stanford University", "MIT", "Yale University", "Princeton University",
                "Columbia University", "University of Pennsylvania", "Duke University", "Northwestern University"
            ],
            "Consulting": [
                "McKinsey & Company", "Boston Consulting Group", "Bain & Company", "Deloitte", "PwC",
                "EY", "KPMG", "Accenture", "IBM Consulting", "Oliver Wyman", "Roland Berger"
            ]
        }
        
        # Realistic locations (major cities worldwide)
        self.locations = [
            "New York, NY", "San Francisco, CA", "Los Angeles, CA", "Chicago, IL", "Boston, MA",
            "Seattle, WA", "Austin, TX", "Denver, CO", "Atlanta, GA", "Miami, FL", "Washington, DC",
            "London, UK", "Paris, France", "Berlin, Germany", "Amsterdam, Netherlands", "Zurich, Switzerland",
            "Toronto, Canada", "Vancouver, Canada", "Sydney, Australia", "Melbourne, Australia",
            "Tokyo, Japan", "Seoul, South Korea", "Singapore", "Hong Kong", "Mumbai, India",
            "Bangalore, India", "Delhi, India", "São Paulo, Brazil", "Mexico City, Mexico",
            "Dubai, UAE", "Tel Aviv, Israel", "Stockholm, Sweden", "Copenhagen, Denmark"
        ]
        
        # Skills by industry
        self.skills = {
            "Technology": [
                "Python", "Java", "JavaScript", "React", "Node.js", "AWS", "Docker", "Kubernetes",
                "Machine Learning", "Data Science", "SQL", "NoSQL", "Git", "Agile", "Scrum",
                "TensorFlow", "PyTorch", "Spark", "Hadoop", "Elasticsearch", "Redis", "MongoDB"
            ],
            "Finance": [
                "Financial Modeling", "Risk Management", "Excel", "Bloomberg Terminal", "SQL",
                "Python", "R", "Tableau", "Power BI", "VBA", "MATLAB", "SAS", "Derivatives",
                "Portfolio Management", "Credit Analysis", "Regulatory Compliance"
            ],
            "Healthcare": [
                "Clinical Research", "Medical Writing", "HIPAA Compliance", "Electronic Health Records",
                "Medical Device Development", "Pharmaceutical Development", "Clinical Trials",
                "Biostatistics", "Epidemiology", "Public Health", "Healthcare Analytics"
            ],
            "Marketing": [
                "Google Analytics", "Facebook Ads", "Google Ads", "SEO", "SEM", "Content Marketing",
                "Social Media Marketing", "Email Marketing", "Marketing Automation", "A/B Testing",
                "Conversion Optimization", "Brand Management", "Market Research", "Adobe Creative Suite"
            ],
            "Education": [
                "Curriculum Development", "Educational Technology", "Learning Management Systems",
                "Assessment Design", "Classroom Management", "Educational Research", "Student Counseling"
            ],
            "Consulting": [
                "Strategy Development", "Business Analysis", "Project Management", "Change Management",
                "Process Improvement", "Data Analysis", "Presentation Skills", "Client Management"
            ]
        }
        
        # Universities by region
        self.universities = [
            "Harvard University", "Stanford University", "MIT", "Yale University", "Princeton University",
            "Columbia University", "University of Pennsylvania", "Duke University", "Northwestern University",
            "University of Chicago", "Cornell University", "Dartmouth College", "Brown University",
            "Vanderbilt University", "Rice University", "Georgetown University", "Carnegie Mellon University",
            "University of California, Berkeley", "UCLA", "University of Southern California",
            "University of Michigan", "University of Virginia", "University of North Carolina",
            "Georgia Institute of Technology", "University of Texas at Austin", "University of Washington",
            "Oxford University", "Cambridge University", "London School of Economics", "Imperial College London",
            "University College London", "King's College London", "University of Edinburgh",
            "Sorbonne University", "ETH Zurich", "Technical University of Munich", "University of Toronto",
            "McGill University", "University of British Columbia", "Australian National University",
            "University of Melbourne", "University of Sydney", "University of Tokyo", "Seoul National University",
            "National University of Singapore", "Indian Institute of Technology", "Indian Institute of Management"
        ]
        
        # Instagram bio templates and interests
        self.instagram_interests = [
            "Photography", "Travel", "Fitness", "Food", "Fashion", "Art", "Music", "Books",
            "Nature", "Technology", "Entrepreneurship", "Wellness", "Yoga", "Running",
            "Cycling", "Hiking", "Cooking", "Coffee", "Wine", "Design", "Architecture",
            "Gaming", "Sports", "Basketball", "Soccer", "Tennis", "Golf", "Skiing",
            "Surfing", "Rock Climbing", "Meditation", "Mindfulness", "Sustainability"
        ]
        
        self.instagram_bio_templates = [
            "{interests} enthusiast | {location_emoji} {city} | {profession_hint}",
            "🌟 {interests} lover | 📍 {city} | {lifestyle_element}",
            "{profession_emoji} {profession_hint} | {interests} addict | {personal_element}",
            "✨ Creating {content_type} | {interests} | {location_emoji} {city}",
            "{personal_element} | {interests} enthusiast | 📱 {tech_element}",
            "🎯 {goal_oriented} | {interests} | {location_emoji} Based in {city}",
            "{lifestyle_element} | {interests} creator | {personal_motto}"
        ]

    def generate_realistic_user_personas(self, num_users: int) -> List[Dict]:
        """Generate realistic user personas with consistent cross-platform identities."""
        personas = []
        
        for i in range(num_users):
            # Basic demographics
            first_name = random.choice(self.first_names)
            last_name = random.choice(self.last_names)
            full_name = f"{first_name} {last_name}"
            
            # Professional information
            industry = random.choice(list(self.industries.keys()))
            job_title = random.choice(self.industries[industry])
            company = random.choice(self.companies[industry])
            location = random.choice(self.locations)
            university = random.choice(self.universities)
            
            # Experience and skills
            experience_years = random.randint(1, 20)
            available_skills = self.skills[industry]
            num_skills = min(random.randint(3, 8), len(available_skills))
            user_skills = random.sample(available_skills, num_skills)
            
            # Personal interests (for Instagram)
            num_interests = min(random.randint(2, 5), len(self.instagram_interests))
            personal_interests = random.sample(self.instagram_interests, num_interests)
            
            # Generate realistic usernames
            username_base = f"{first_name.lower()}{last_name.lower()}"
            username_variations = [
                username_base,
                f"{first_name.lower()}.{last_name.lower()}",
                f"{first_name.lower()}_{last_name.lower()}",
                f"{first_name.lower()}{last_name.lower()}{random.randint(1, 999)}",
                f"{first_name[0].lower()}{last_name.lower()}",
                f"{first_name.lower()}{last_name[0].lower()}",
                f"{username_base}{random.randint(10, 99)}"
            ]
            
            # Create persona
            persona = {
                "id": i,
                "first_name": first_name,
                "last_name": last_name,
                "full_name": full_name,
                "industry": industry,
                "job_title": job_title,
                "company": company,
                "location": location,
                "university": university,
                "experience_years": experience_years,
                "skills": user_skills,
                "personal_interests": personal_interests,
                "username_variations": username_variations,
                "email_domain": random.choice(["gmail.com", "outlook.com", "yahoo.com", "company.com"]),
                "age_range": random.choice(["22-28", "29-35", "36-42", "43-50", "51-60"]),
                "education_level": random.choice(["Bachelor's", "Master's", "PhD", "MBA"]),
                "personality_traits": random.sample([
                    "analytical", "creative", "leadership", "collaborative", "innovative",
                    "detail-oriented", "strategic", "entrepreneurial", "technical", "communicative"
                ], random.randint(2, 4))
            }
            
            personas.append(persona)

        return personas

    def generate_linkedin_profiles(self, personas: List[Dict], match_ratio: float = 0.8) -> pd.DataFrame:
        """Generate realistic LinkedIn profiles from personas."""
        linkedin_profiles = []

        # Determine which personas will have LinkedIn profiles
        num_linkedin_users = int(len(personas) * match_ratio)
        linkedin_personas = random.sample(personas, num_linkedin_users)

        for persona in linkedin_personas:
            # Generate realistic LinkedIn user ID
            username_base = random.choice(persona["username_variations"])
            linkedin_user_id = f"linkedin_{username_base}"

            # Generate professional bio/summary
            bio_templates = [
                f"{persona['job_title']} at {persona['company']} | {persona['experience_years']}+ years in {persona['industry']} | Passionate about {', '.join(persona['skills'][:3])}",
                f"Experienced {persona['job_title']} | {persona['industry']} professional | {persona['university']} alumnus | Specializing in {', '.join(persona['skills'][:2])}",
                f"Senior {persona['job_title']} with {persona['experience_years']} years of experience | Leading {persona['industry']} initiatives at {persona['company']} | Expert in {', '.join(persona['skills'][:3])}",
                f"{persona['job_title']} | {persona['company']} | Driving innovation in {persona['industry']} | {', '.join(persona['personality_traits'])} leader",
                f"Passionate {persona['job_title']} | {persona['experience_years']}+ years transforming {persona['industry']} | {persona['university']} graduate | {', '.join(persona['skills'][:2])} expert"
            ]

            bio = random.choice(bio_templates)

            # Generate realistic metrics
            base_followers = {
                "Technology": random.randint(800, 5000),
                "Finance": random.randint(600, 3000),
                "Healthcare": random.randint(400, 2000),
                "Marketing": random.randint(1000, 6000),
                "Education": random.randint(300, 1500),
                "Consulting": random.randint(700, 4000)
            }

            followers_count = base_followers[persona["industry"]] + random.randint(-200, 1000)
            connections_count = min(followers_count * random.uniform(0.3, 0.8), 500)  # LinkedIn connection limit consideration

            # Generate posts count based on activity level
            activity_level = random.choice(["low", "medium", "high"])
            posts_count = {
                "low": random.randint(5, 25),
                "medium": random.randint(26, 100),
                "high": random.randint(101, 500)
            }[activity_level]

            profile_views = followers_count * random.uniform(0.1, 0.3)

            # Create LinkedIn profile
            linkedin_profile = {
                "user_id": linkedin_user_id,
                "username": username_base,
                "name": persona["full_name"],
                "title": persona["job_title"],
                "company": persona["company"],
                "location": persona["location"],
                "bio": bio,
                "industry": persona["industry"],
                "experience_years": persona["experience_years"],
                "education": persona["university"],
                "skills": ", ".join(persona["skills"]),
                "followers_count": int(followers_count),
                "connections_count": int(connections_count),
                "posts_count": posts_count,
                "profile_views": int(profile_views),
                "verified": random.choice([True, False]) if followers_count > 2000 else False,
                "premium": random.choice([True, False]) if random.random() < 0.3 else False,
                "created_date": self._generate_realistic_date("2010-01-01", "2020-12-31"),
                "last_active": self._generate_realistic_date("2024-01-01", "2024-12-20"),
                "profile_url": f"linkedin.com/in/{username_base}",
                "persona_id": persona["id"]  # For ground truth mapping
            }

            linkedin_profiles.append(linkedin_profile)

        return pd.DataFrame(linkedin_profiles)

    def generate_instagram_profiles(self, personas: List[Dict], linkedin_df: pd.DataFrame,
                                  cross_platform_ratio: float = 0.7) -> pd.DataFrame:
        """Generate realistic Instagram profiles with cross-platform variations."""
        instagram_profiles = []

        # Get LinkedIn personas for cross-platform matching
        linkedin_persona_ids = set(linkedin_df["persona_id"].tolist())

        # Determine cross-platform users
        num_cross_platform = int(len(linkedin_persona_ids) * cross_platform_ratio)
        cross_platform_persona_ids = random.sample(list(linkedin_persona_ids), num_cross_platform)

        # Add some Instagram-only users
        remaining_personas = [p for p in personas if p["id"] not in linkedin_persona_ids]
        num_instagram_only = random.randint(50, 150)
        instagram_only_personas = random.sample(remaining_personas,
                                               min(num_instagram_only, len(remaining_personas)))

        # Generate cross-platform Instagram profiles
        for persona_id in cross_platform_persona_ids:
            persona = next(p for p in personas if p["id"] == persona_id)
            instagram_profile = self._create_instagram_profile(persona, is_cross_platform=True)
            instagram_profiles.append(instagram_profile)

        # Generate Instagram-only profiles
        for persona in instagram_only_personas:
            instagram_profile = self._create_instagram_profile(persona, is_cross_platform=False)
            instagram_profiles.append(instagram_profile)

        return pd.DataFrame(instagram_profiles)

    def _create_instagram_profile(self, persona: Dict, is_cross_platform: bool) -> Dict:
        """Create a single Instagram profile with realistic variations."""

        # Generate Instagram username (often different from LinkedIn)
        if is_cross_platform and random.random() < 0.6:  # 60% chance of similar username
            base_username = random.choice(persona["username_variations"])
            # Add Instagram-specific variations
            instagram_variations = [
                base_username,
                f"{base_username}_",
                f"_{base_username}",
                f"{base_username}{random.randint(10, 99)}",
                f"{persona['first_name'].lower()}_{persona['last_name'].lower()}",
                f"{persona['first_name'].lower()}.{persona['last_name'].lower()}",
                f"{persona['first_name'].lower()}{random.randint(100, 999)}"
            ]
            username = random.choice(instagram_variations)
        else:
            # Completely different username
            username = f"{persona['first_name'].lower()}{random.choice(['_', '.', ''])}{random.choice(['life', 'vibes', 'world', 'journey', 'adventures'])}{random.randint(1, 99)}"

        instagram_user_id = f"instagram_{username}"

        # Generate Instagram bio (more personal/casual)
        city = persona["location"].split(",")[0]  # Extract city from location

        bio_elements = {
            "interests": random.choice(persona["personal_interests"]),
            "location_emoji": random.choice(["📍", "🌍", "🏙️", "🌆"]),
            "city": city,
            "profession_hint": self._get_casual_profession_hint(persona["job_title"]),
            "profession_emoji": self._get_profession_emoji(persona["industry"]),
            "lifestyle_element": random.choice(["Living my best life", "Creating memories", "Chasing dreams", "Making it happen"]),
            "personal_element": random.choice(["Coffee addict ☕", "Dog lover 🐕", "Foodie 🍕", "Bookworm 📚", "Music lover 🎵"]),
            "content_type": random.choice(["content", "memories", "stories", "moments"]),
            "tech_element": random.choice(["Tech enthusiast", "Digital nomad", "Innovation lover"]),
            "goal_oriented": random.choice(["Entrepreneur", "Creator", "Innovator", "Dreamer"]),
            "personal_motto": random.choice(["Stay curious", "Keep exploring", "Never stop learning", "Dream big"])
        }

        bio_template = random.choice(self.instagram_bio_templates)
        bio = bio_template.format(**bio_elements)

        # Generate realistic Instagram metrics
        base_followers = random.randint(200, 3000)
        if persona["industry"] in ["Marketing", "Technology"]:
            base_followers = random.randint(500, 5000)

        followers_count = base_followers + random.randint(-100, 1000)
        following_count = random.randint(100, 1500)
        posts_count = random.randint(10, 500)
        stories_count = random.randint(0, 100)
        reels_count = random.randint(0, 50)

        # Engagement metrics
        avg_likes = int(followers_count * random.uniform(0.02, 0.15))  # 2-15% engagement
        avg_comments = int(avg_likes * random.uniform(0.05, 0.2))  # 5-20% of likes
        engagement_rate = (avg_likes + avg_comments) / followers_count * 100 if followers_count > 0 else 0

        # Account settings
        is_private = random.choice([True, False]) if random.random() < 0.3 else False
        is_verified = random.choice([True, False]) if followers_count > 10000 else False
        business_account = random.choice([True, False]) if random.random() < 0.2 else False

        return {
            "user_id": instagram_user_id,
            "username": username,
            "name": persona["full_name"] if random.random() < 0.8 else persona["first_name"],  # Sometimes just first name
            "bio": bio,
            "location": persona["location"] if random.random() < 0.6 else "",  # Not always shared
            "followers_count": followers_count,
            "following_count": following_count,
            "posts_count": posts_count,
            "stories_count": stories_count,
            "reels_count": reels_count,
            "avg_likes": avg_likes,
            "avg_comments": avg_comments,
            "engagement_rate": round(engagement_rate, 2),
            "is_private": is_private,
            "is_verified": is_verified,
            "business_account": business_account,
            "created_date": self._generate_realistic_date("2012-01-01", "2022-12-31"),
            "last_post_date": self._generate_realistic_date("2024-01-01", "2024-12-20"),
            "profile_url": f"instagram.com/{username}",
            "persona_id": persona["id"]  # For ground truth mapping
        }

    def _get_casual_profession_hint(self, job_title: str) -> str:
        """Convert formal job title to casual Instagram-friendly hint."""
        casual_hints = {
            "Software Engineer": "👨‍💻 Tech enthusiast",
            "Data Scientist": "📊 Data lover",
            "Product Manager": "🚀 Product builder",
            "UX Designer": "🎨 Design thinker",
            "Marketing Manager": "📱 Marketing guru",
            "Financial Analyst": "💼 Finance pro",
            "Consultant": "💡 Problem solver",
            "Teacher": "📚 Educator",
            "Physician": "🩺 Healthcare hero"
        }

        for key, hint in casual_hints.items():
            if key.lower() in job_title.lower():
                return hint

        return "💼 Professional"

    def _get_profession_emoji(self, industry: str) -> str:
        """Get emoji representing the industry."""
        emoji_map = {
            "Technology": "💻",
            "Finance": "💰",
            "Healthcare": "🏥",
            "Marketing": "📱",
            "Education": "📚",
            "Consulting": "💼"
        }
        return emoji_map.get(industry, "💼")

    def _generate_realistic_date(self, start_date: str, end_date: str) -> str:
        """Generate a realistic date between start and end dates."""
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")

        time_between = end - start
        days_between = time_between.days
        random_days = random.randrange(days_between)

        random_date = start + timedelta(days=random_days)
        return random_date.strftime("%Y-%m-%d")

    def generate_linkedin_posts(self, linkedin_df: pd.DataFrame, personas: List[Dict]) -> pd.DataFrame:
        """Generate realistic LinkedIn posts."""
        posts = []

        # Create persona lookup
        persona_lookup = {p["id"]: p for p in personas}

        for _, profile in linkedin_df.iterrows():
            persona = persona_lookup[profile["persona_id"]]
            max_posts = min(profile["posts_count"], 50)
            num_posts = random.randint(1, max(max_posts, 1))  # Ensure at least 1 post

            for i in range(num_posts):
                post_content = self._generate_linkedin_post_content(persona, profile)

                # Generate realistic engagement
                base_likes = int(profile["followers_count"] * random.uniform(0.01, 0.1))
                likes = max(1, base_likes + random.randint(-base_likes//2, base_likes))
                comments = max(0, int(likes * random.uniform(0.02, 0.15)))

                # Generate timestamp
                post_date = self._generate_realistic_date("2023-01-01", "2024-12-15")
                timestamp = f"{post_date} {random.randint(8, 20):02d}:{random.randint(0, 59):02d}:00"

                post = {
                    "post_id": f"{profile['user_id']}_post_{i+1}",
                    "user_id": profile["user_id"],
                    "content": post_content,
                    "timestamp": timestamp,
                    "likes": likes,
                    "comments": comments,
                    "shares": max(0, int(likes * random.uniform(0.01, 0.05))),
                    "post_type": random.choice(["text", "article", "image", "video", "poll"])
                }

                posts.append(post)

        return pd.DataFrame(posts)

    def _generate_linkedin_post_content(self, persona: Dict, profile: Dict) -> str:
        """Generate realistic LinkedIn post content based on persona."""

        post_templates = {
            "achievement": [
                f"Excited to share that our team at {persona['company']} just {random.choice(['launched', 'completed', 'achieved'])} {random.choice(['a major project', 'an important milestone', 'our quarterly goals'])}! {random.choice(['Proud of', 'Grateful for', 'Thrilled about'])} the collaboration and innovation.",
                f"Thrilled to announce that I've been {random.choice(['promoted to', 'selected for', 'invited to join'])} {random.choice(['a leadership role', 'an exciting project', 'a new initiative'])} at {persona['company']}. Looking forward to the challenges ahead!",
                f"Just {random.choice(['completed', 'finished', 'graduated from'])} an amazing {random.choice(['certification program', 'training course', 'workshop'])} in {random.choice(persona['skills'])}. Always learning and growing!"
            ],
            "industry_insight": [
                f"The {persona['industry']} industry is rapidly evolving. Here are my thoughts on {random.choice(['emerging trends', 'key challenges', 'future opportunities'])} and how {random.choice(persona['skills'])} will play a crucial role.",
                f"Interesting discussion at today's {persona['industry']} conference about {random.choice(['digital transformation', 'innovation strategies', 'market trends'])}. Key takeaway: {random.choice(['collaboration is key', 'data drives decisions', 'customer focus wins'])}.",
                f"As a {persona['job_title']}, I've noticed that {random.choice(['automation', 'AI', 'data analytics'])} is transforming how we approach {random.choice(['problem-solving', 'decision-making', 'strategy development'])}."
            ],
            "thought_leadership": [
                f"After {persona['experience_years']} years in {persona['industry']}, I believe that {random.choice(['innovation', 'collaboration', 'continuous learning'])} is the key to success. What's your perspective?",
                f"The future of {persona['industry']} lies in {random.choice(['sustainable practices', 'digital innovation', 'human-centered design'])}. How is your organization adapting?",
                f"Reflecting on my journey from {persona['university']} to {persona['company']}, I've learned that {random.choice(['persistence', 'adaptability', 'curiosity'])} matters most."
            ],
            "team_appreciation": [
                f"Shoutout to my amazing team at {persona['company']}! Their expertise in {random.choice(persona['skills'])} made our latest project a huge success. #TeamWork",
                f"Grateful to work with such talented professionals who share my passion for {random.choice(persona['skills'])}. Together, we're making a real impact!",
                f"The best part of being a {persona['job_title']} is collaborating with brilliant minds who challenge and inspire me every day."
            ]
        }

        post_type = random.choice(list(post_templates.keys()))
        template = random.choice(post_templates[post_type])

        # Add relevant hashtags
        hashtags = [
            f"#{persona['industry'].replace(' ', '')}",
            f"#{persona['job_title'].replace(' ', '')}",
            f"#{random.choice(persona['skills']).replace(' ', '')}",
            "#Leadership", "#Innovation", "#Growth", "#Career"
        ]

        selected_hashtags = random.sample(hashtags, random.randint(2, 4))

        return f"{template}\n\n{' '.join(selected_hashtags)}"

    def generate_instagram_posts(self, instagram_df: pd.DataFrame, personas: List[Dict]) -> pd.DataFrame:
        """Generate realistic Instagram posts."""
        posts = []

        # Create persona lookup
        persona_lookup = {p["id"]: p for p in personas}

        for _, profile in instagram_df.iterrows():
            persona = persona_lookup[profile["persona_id"]]
            max_posts = min(profile["posts_count"], 30)
            num_posts = random.randint(1, max(max_posts, 1))  # Ensure at least 1 post

            for i in range(num_posts):
                post_content = self._generate_instagram_post_content(persona, profile)

                # Generate realistic engagement (higher than LinkedIn typically)
                base_likes = profile["avg_likes"]
                likes = max(1, base_likes + random.randint(-base_likes//3, base_likes//2))
                comments = max(0, int(likes * random.uniform(0.03, 0.2)))

                # Generate timestamp
                post_date = self._generate_realistic_date("2023-06-01", "2024-12-15")
                timestamp = f"{post_date} {random.randint(6, 23):02d}:{random.randint(0, 59):02d}:00"

                post = {
                    "post_id": f"{profile['user_id']}_post_{i+1}",
                    "user_id": profile["user_id"],
                    "content": post_content,
                    "timestamp": timestamp,
                    "likes": likes,
                    "comments": comments,
                    "shares": max(0, int(likes * random.uniform(0.005, 0.02))),
                    "post_type": random.choice(["photo", "video", "carousel", "reel", "story"])
                }

                posts.append(post)

        return pd.DataFrame(posts)

    def _generate_instagram_post_content(self, persona: Dict, profile: Dict) -> str:
        """Generate realistic Instagram post content based on persona."""

        post_templates = {
            "lifestyle": [
                f"Perfect {random.choice(['morning', 'evening', 'weekend'])} vibes ✨ {random.choice(['#blessed', '#grateful', '#livingmybestlife'])}",
                f"Exploring {random.choice(['new places', 'hidden gems', 'local favorites'])} in {persona['location'].split(',')[0]} 🌟",
                f"{random.choice(['Coffee', 'Sunset', 'Adventure'])} time! {random.choice(['☕', '🌅', '🌄'])} {random.choice(['#mood', '#vibes', '#life'])}"
            ],
            "professional": [
                f"Excited about {random.choice(['new projects', 'upcoming challenges', 'team collaborations'])} at work! {random.choice(['💼', '🚀', '💡'])} #{persona['industry'].replace(' ', '').lower()}",
                f"Learning never stops! Just discovered something amazing about {random.choice(persona['skills'])} 📚 #growth",
                f"Grateful for the opportunity to work in {persona['industry']} and make a difference! 🙏"
            ],
            "personal_interest": [
                f"Spent the day {random.choice(['hiking', 'reading', 'cooking', 'exploring'])} - exactly what I needed! {random.choice(['🥾', '📖', '👨‍🍳', '🗺️'])}",
                f"Nothing beats a good {random.choice(persona['personal_interests'])} session! {random.choice(['💪', '🎨', '📸', '🎵'])} #passion",
                f"Weekend plans: more {random.choice(persona['personal_interests'])} and good vibes! ✨"
            ],
            "motivational": [
                f"Remember: {random.choice(['every day is a new opportunity', 'progress over perfection', 'small steps lead to big changes'])} 💫",
                f"Feeling {random.choice(['inspired', 'motivated', 'grateful'])} today! What's driving you? 🌟",
                f"Chase your dreams and make them happen! {random.choice(['💪', '🚀', '✨'])} #motivation"
            ]
        }

        post_type = random.choice(list(post_templates.keys()))
        template = random.choice(post_templates[post_type])

        # Add relevant hashtags
        hashtags = [
            f"#{random.choice(persona['personal_interests']).lower()}",
            f"#{persona['location'].split(',')[0].lower().replace(' ', '')}",
            "#instagood", "#photooftheday", "#life", "#happy", "#love"
        ]

        selected_hashtags = random.sample(hashtags, random.randint(2, 5))

        return f"{template}\n\n{' '.join(selected_hashtags)}"

    def generate_networks(self, linkedin_df: pd.DataFrame, instagram_df: pd.DataFrame,
                         personas: List[Dict]) -> Tuple[nx.Graph, nx.Graph]:
        """Generate realistic social networks for both platforms."""

        # Create persona lookup
        persona_lookup = {p["id"]: p for p in personas}

        # Generate LinkedIn network (professional connections)
        linkedin_network = self._generate_linkedin_network(linkedin_df, persona_lookup)

        # Generate Instagram network (personal/interest-based connections)
        instagram_network = self._generate_instagram_network(instagram_df, persona_lookup)

        return linkedin_network, instagram_network

    def _generate_linkedin_network(self, linkedin_df: pd.DataFrame, persona_lookup: Dict) -> nx.Graph:
        """Generate realistic LinkedIn professional network."""
        G = nx.Graph()

        # Add all users as nodes
        user_ids = linkedin_df["user_id"].tolist()
        G.add_nodes_from(user_ids)

        # Group users by industry and company for realistic connections
        industry_groups = linkedin_df.groupby("industry")["user_id"].apply(list).to_dict()
        company_groups = linkedin_df.groupby("company")["user_id"].apply(list).to_dict()

        # Add connections within same industry (higher probability)
        for industry, users in industry_groups.items():
            if len(users) > 1:
                for i, user1 in enumerate(users):
                    for user2 in users[i+1:]:
                        if random.random() < 0.3:  # 30% chance of industry connection
                            G.add_edge(user1, user2, weight=1, connection_type="industry")

        # Add connections within same company (very high probability)
        for company, users in company_groups.items():
            if len(users) > 1:
                for i, user1 in enumerate(users):
                    for user2 in users[i+1:]:
                        if random.random() < 0.8:  # 80% chance of company connection
                            G.add_edge(user1, user2, weight=1, connection_type="company")

        # Add random professional connections
        for user in user_ids:
            num_random_connections = random.randint(5, 25)
            potential_connections = [u for u in user_ids if u != user and not G.has_edge(user, u)]

            if potential_connections:
                random_connections = random.sample(
                    potential_connections,
                    min(num_random_connections, len(potential_connections))
                )
                for connection in random_connections:
                    if random.random() < 0.1:  # 10% chance of random connection
                        G.add_edge(user, connection, weight=1, connection_type="professional")

        return G

    def _generate_instagram_network(self, instagram_df: pd.DataFrame, persona_lookup: Dict) -> nx.Graph:
        """Generate realistic Instagram social network."""
        G = nx.Graph()

        # Add all users as nodes
        user_ids = instagram_df["user_id"].tolist()
        G.add_nodes_from(user_ids)

        # Group users by location and interests for realistic connections
        location_groups = {}
        interest_groups = {}

        for _, profile in instagram_df.iterrows():
            persona = persona_lookup[profile["persona_id"]]

            # Group by location
            location = persona["location"].split(",")[0]  # City only
            if location not in location_groups:
                location_groups[location] = []
            location_groups[location].append(profile["user_id"])

            # Group by interests
            for interest in persona["personal_interests"]:
                if interest not in interest_groups:
                    interest_groups[interest] = []
                interest_groups[interest].append(profile["user_id"])

        # Add connections within same location (moderate probability)
        for location, users in location_groups.items():
            if len(users) > 1:
                for i, user1 in enumerate(users):
                    for user2 in users[i+1:]:
                        if random.random() < 0.2:  # 20% chance of location-based connection
                            G.add_edge(user1, user2, weight=1, connection_type="location")

        # Add connections based on shared interests (high probability)
        for interest, users in interest_groups.items():
            if len(users) > 1:
                for i, user1 in enumerate(users):
                    for user2 in users[i+1:]:
                        if random.random() < 0.4:  # 40% chance of interest-based connection
                            G.add_edge(user1, user2, weight=1, connection_type="interest")

        # Add random social connections (follows)
        for user in user_ids:
            num_random_follows = random.randint(10, 50)
            potential_follows = [u for u in user_ids if u != user and not G.has_edge(user, u)]

            if potential_follows:
                random_follows = random.sample(
                    potential_follows,
                    min(num_random_follows, len(potential_follows))
                )
                for follow in random_follows:
                    if random.random() < 0.15:  # 15% chance of random follow
                        G.add_edge(user, follow, weight=1, connection_type="social")

        return G

    def generate_ground_truth(self, linkedin_df: pd.DataFrame, instagram_df: pd.DataFrame) -> pd.DataFrame:
        """Generate ground truth mappings between LinkedIn and Instagram profiles."""
        ground_truth = []

        # Find matching personas between platforms
        linkedin_personas = set(linkedin_df["persona_id"].tolist())
        instagram_personas = set(instagram_df["persona_id"].tolist())
        matching_personas = linkedin_personas.intersection(instagram_personas)

        for persona_id in matching_personas:
            linkedin_user = linkedin_df[linkedin_df["persona_id"] == persona_id].iloc[0]
            instagram_user = instagram_df[instagram_df["persona_id"] == persona_id].iloc[0]

            # Calculate match difficulty based on profile similarities
            difficulty = self._calculate_match_difficulty(linkedin_user, instagram_user)

            ground_truth.append({
                "linkedin_id": linkedin_user["user_id"],
                "instagram_id": instagram_user["user_id"],
                "persona_id": persona_id,
                "match_confidence": 1.0,  # True positive
                "difficulty_level": difficulty,
                "match_type": "cross_platform"
            })

        # Add some challenging negative examples (different personas with similar characteristics)
        negative_samples = self._generate_negative_samples(linkedin_df, instagram_df, len(ground_truth) // 4)
        ground_truth.extend(negative_samples)

        return pd.DataFrame(ground_truth)

    def _calculate_match_difficulty(self, linkedin_user: pd.Series, instagram_user: pd.Series) -> str:
        """Calculate the difficulty level of matching based on profile similarities."""

        difficulty_score = 0

        # Name similarity
        if linkedin_user["name"].lower() != instagram_user["name"].lower():
            difficulty_score += 2

        # Username similarity
        linkedin_username = linkedin_user["username"]
        instagram_username = instagram_user["username"]
        if linkedin_username not in instagram_username and instagram_username not in linkedin_username:
            difficulty_score += 3

        # Bio/content similarity (simplified check)
        linkedin_bio = linkedin_user["bio"].lower()
        instagram_bio = instagram_user["bio"].lower()
        common_words = set(linkedin_bio.split()) & set(instagram_bio.split())
        if len(common_words) < 3:
            difficulty_score += 2

        # Location similarity
        if linkedin_user.get("location", "") != instagram_user.get("location", ""):
            difficulty_score += 1

        # Determine difficulty level
        if difficulty_score <= 2:
            return "easy"
        elif difficulty_score <= 5:
            return "medium"
        else:
            return "hard"

    def _generate_negative_samples(self, linkedin_df: pd.DataFrame, instagram_df: pd.DataFrame,
                                 num_samples: int) -> List[Dict]:
        """Generate negative samples (non-matching pairs) for evaluation."""
        negative_samples = []

        linkedin_users = linkedin_df.sample(n=min(num_samples, len(linkedin_df)))
        instagram_users = instagram_df.sample(n=min(num_samples, len(instagram_df)))

        for i in range(min(len(linkedin_users), len(instagram_users))):
            linkedin_user = linkedin_users.iloc[i]
            instagram_user = instagram_users.iloc[i]

            # Ensure they're actually different personas
            if linkedin_user["persona_id"] != instagram_user["persona_id"]:
                negative_samples.append({
                    "linkedin_id": linkedin_user["user_id"],
                    "instagram_id": instagram_user["user_id"],
                    "persona_id": -1,  # Indicates no match
                    "match_confidence": 0.0,  # True negative
                    "difficulty_level": "negative",
                    "match_type": "no_match"
                })

        return negative_samples

    def save_datasets(self, output_dir: str, linkedin_df: pd.DataFrame, instagram_df: pd.DataFrame,
                     linkedin_posts: pd.DataFrame, instagram_posts: pd.DataFrame,
                     linkedin_network: nx.Graph, instagram_network: nx.Graph,
                     ground_truth: pd.DataFrame):
        """Save all generated datasets to files."""

        # Create output directories
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, "linkedin"), exist_ok=True)
        os.makedirs(os.path.join(output_dir, "instagram"), exist_ok=True)

        # Save LinkedIn data
        linkedin_df.to_csv(os.path.join(output_dir, "linkedin", "profiles.csv"), index=False)
        linkedin_posts.to_csv(os.path.join(output_dir, "linkedin", "posts.csv"), index=False)

        # Save LinkedIn network as both CSV and edgelist
        linkedin_edges = [(u, v, d.get('weight', 1)) for u, v, d in linkedin_network.edges(data=True)]
        linkedin_network_df = pd.DataFrame(linkedin_edges, columns=["source", "target", "weight"])
        linkedin_network_df.to_csv(os.path.join(output_dir, "linkedin", "network.csv"), index=False)
        nx.write_edgelist(linkedin_network, os.path.join(output_dir, "linkedin", "network.edgelist"))

        # Save Instagram data
        instagram_df.to_csv(os.path.join(output_dir, "instagram", "profiles.csv"), index=False)
        instagram_posts.to_csv(os.path.join(output_dir, "instagram", "posts.csv"), index=False)

        # Save Instagram network as both CSV and edgelist
        instagram_edges = [(u, v, d.get('weight', 1)) for u, v, d in instagram_network.edges(data=True)]
        instagram_network_df = pd.DataFrame(instagram_edges, columns=["source", "target", "weight"])
        instagram_network_df.to_csv(os.path.join(output_dir, "instagram", "network.csv"), index=False)
        nx.write_edgelist(instagram_network, os.path.join(output_dir, "instagram", "network.edgelist"))

        # Save ground truth
        ground_truth.to_csv(os.path.join(output_dir, "ground_truth.csv"), index=False)

        # Save dataset statistics
        stats = self._generate_dataset_statistics(linkedin_df, instagram_df, linkedin_posts,
                                                instagram_posts, linkedin_network, instagram_network, ground_truth)

        with open(os.path.join(output_dir, "dataset_statistics.json"), "w") as f:
            json.dump(stats, f, indent=2)

        print(f"✅ Dataset saved to: {output_dir}")
        print(f"📊 LinkedIn profiles: {len(linkedin_df)}")
        print(f"📊 Instagram profiles: {len(instagram_df)}")
        print(f"📊 Ground truth matches: {len(ground_truth[ground_truth['match_confidence'] > 0])}")
        print(f"📊 Total posts: {len(linkedin_posts) + len(instagram_posts)}")
        print(f"📊 Network connections: LinkedIn={linkedin_network.number_of_edges()}, Instagram={instagram_network.number_of_edges()}")

    def _generate_dataset_statistics(self, linkedin_df: pd.DataFrame, instagram_df: pd.DataFrame,
                                   linkedin_posts: pd.DataFrame, instagram_posts: pd.DataFrame,
                                   linkedin_network: nx.Graph, instagram_network: nx.Graph,
                                   ground_truth: pd.DataFrame) -> Dict:
        """Generate comprehensive dataset statistics."""

        stats = {
            "dataset_info": {
                "generation_date": datetime.now().isoformat(),
                "total_personas": len(set(linkedin_df["persona_id"].tolist() + instagram_df["persona_id"].tolist())),
                "cross_platform_users": len(ground_truth[ground_truth["match_confidence"] > 0])
            },
            "linkedin": {
                "profiles": len(linkedin_df),
                "posts": len(linkedin_posts),
                "network_nodes": linkedin_network.number_of_nodes(),
                "network_edges": linkedin_network.number_of_edges(),
                "avg_connections": float(linkedin_df["connections_count"].mean()),
                "avg_followers": float(linkedin_df["followers_count"].mean()),
                "industries": linkedin_df["industry"].value_counts().to_dict()
            },
            "instagram": {
                "profiles": len(instagram_df),
                "posts": len(instagram_posts),
                "network_nodes": instagram_network.number_of_nodes(),
                "network_edges": instagram_network.number_of_edges(),
                "avg_followers": float(instagram_df["followers_count"].mean()),
                "avg_following": float(instagram_df["following_count"].mean()),
                "private_accounts": int(instagram_df["is_private"].sum()),
                "verified_accounts": int(instagram_df["is_verified"].sum())
            },
            "ground_truth": {
                "total_pairs": len(ground_truth),
                "positive_matches": len(ground_truth[ground_truth["match_confidence"] > 0]),
                "negative_samples": len(ground_truth[ground_truth["match_confidence"] == 0]),
                "difficulty_distribution": ground_truth["difficulty_level"].value_counts().to_dict()
            }
        }

        return stats

    def add_challenging_edge_cases(self, linkedin_df: pd.DataFrame, instagram_df: pd.DataFrame,
                                 personas: List[Dict]) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Add challenging edge cases to make the dataset more realistic and difficult."""

        # Create copies to modify
        linkedin_modified = linkedin_df.copy()
        instagram_modified = instagram_df.copy()

        # Edge Case 1: Name variations (married names, nicknames, etc.)
        name_variation_indices = random.sample(range(len(linkedin_modified)), len(linkedin_modified) // 10)
        for idx in name_variation_indices:
            if idx < len(instagram_modified):
                # Simulate name changes (marriage, nicknames, etc.)
                original_name = linkedin_modified.iloc[idx]["name"]
                variations = [
                    original_name.split()[0] + " " + random.choice(self.last_names),  # Married name
                    original_name.split()[0],  # First name only
                    original_name.split()[0] + " " + original_name.split()[0][0] + ".",  # First name + initial
                    original_name.replace(original_name.split()[0], random.choice(["Alex", "Sam", "Chris", "Jordan"])),  # Nickname
                ]
                instagram_modified.iloc[idx, instagram_modified.columns.get_loc("name")] = random.choice(variations)

        # Edge Case 2: Completely different usernames
        username_variation_indices = random.sample(range(len(linkedin_modified)), len(linkedin_modified) // 8)
        for idx in username_variation_indices:
            if idx < len(instagram_modified):
                # Generate completely unrelated username
                random_usernames = [
                    f"{random.choice(['sunset', 'ocean', 'mountain', 'star', 'moon'])}_{random.randint(100, 999)}",
                    f"{random.choice(['creative', 'artistic', 'wanderer', 'dreamer'])}_{random.choice(['soul', 'heart', 'mind'])}",
                    f"{random.choice(['photo', 'travel', 'food', 'music'])}_{random.choice(['lover', 'addict', 'enthusiast'])}{random.randint(10, 99)}"
                ]
                new_username = random.choice(random_usernames)
                instagram_modified.iloc[idx, instagram_modified.columns.get_loc("username")] = new_username
                instagram_modified.iloc[idx, instagram_modified.columns.get_loc("user_id")] = f"instagram_{new_username}"

        # Edge Case 3: Privacy settings (limited information)
        private_indices = random.sample(range(len(instagram_modified)), len(instagram_modified) // 6)
        for idx in private_indices:
            # Make account private with limited bio
            instagram_modified.iloc[idx, instagram_modified.columns.get_loc("is_private")] = True
            instagram_modified.iloc[idx, instagram_modified.columns.get_loc("bio")] = random.choice([
                "🔒 Private account", "✨", "📱", "🌟 Living life", "🎯", "💫"
            ])
            # Reduce follower count for private accounts
            current_followers = instagram_modified.iloc[idx]["followers_count"]
            instagram_modified.iloc[idx, instagram_modified.columns.get_loc("followers_count")] = int(current_followers * 0.3)

        # Edge Case 4: Inactive users (old posts, low activity)
        inactive_indices = random.sample(range(len(linkedin_modified)), len(linkedin_modified) // 12)
        for idx in inactive_indices:
            # Reduce activity metrics
            linkedin_modified.iloc[idx, linkedin_modified.columns.get_loc("posts_count")] = random.randint(1, 5)
            linkedin_modified.iloc[idx, linkedin_modified.columns.get_loc("last_active")] = self._generate_realistic_date("2022-01-01", "2023-06-01")

            if idx < len(instagram_modified):
                instagram_modified.iloc[idx, instagram_modified.columns.get_loc("posts_count")] = random.randint(1, 8)
                instagram_modified.iloc[idx, instagram_modified.columns.get_loc("last_post_date")] = self._generate_realistic_date("2022-01-01", "2023-06-01")

        # Edge Case 5: Professional vs Personal content mismatch
        content_mismatch_indices = random.sample(range(len(linkedin_modified)), len(linkedin_modified) // 15)
        for idx in content_mismatch_indices:
            if idx < len(instagram_modified):
                # LinkedIn shows one profession, Instagram suggests another interest/hobby
                persona_id = linkedin_modified.iloc[idx]["persona_id"]
                persona = next(p for p in personas if p["id"] == persona_id)

                # Change Instagram bio to focus on hobbies rather than profession
                hobby_focused_bios = [
                    f"🎨 {random.choice(persona['personal_interests'])} enthusiast | Weekend warrior",
                    f"📸 Capturing life's moments | {random.choice(persona['personal_interests'])} lover",
                    f"🌍 Explorer | {random.choice(persona['personal_interests'])} addict | Living authentically",
                    f"✨ {random.choice(persona['personal_interests'])} | Coffee & adventures ☕🌄"
                ]
                instagram_modified.iloc[idx, instagram_modified.columns.get_loc("bio")] = random.choice(hobby_focused_bios)

        # Edge Case 6: Location inconsistencies
        location_mismatch_indices = random.sample(range(len(linkedin_modified)), len(linkedin_modified) // 20)
        for idx in location_mismatch_indices:
            if idx < len(instagram_modified):
                # Different locations (moved, travel, etc.)
                new_location = random.choice(self.locations)
                instagram_modified.iloc[idx, instagram_modified.columns.get_loc("location")] = new_location if random.random() < 0.7 else ""

        # Edge Case 7: Engagement anomalies
        engagement_anomaly_indices = random.sample(range(len(instagram_modified)), len(instagram_modified) // 10)
        for idx in engagement_anomaly_indices:
            # Unusual engagement patterns (bought followers, viral content, etc.)
            followers = instagram_modified.iloc[idx]["followers_count"]
            if random.random() < 0.5:
                # High followers, low engagement (bought followers)
                instagram_modified.iloc[idx, instagram_modified.columns.get_loc("followers_count")] = followers * 3
                instagram_modified.iloc[idx, instagram_modified.columns.get_loc("avg_likes")] = int(followers * 0.005)  # Very low engagement
            else:
                # Low followers, high engagement (viral content)
                instagram_modified.iloc[idx, instagram_modified.columns.get_loc("avg_likes")] = int(followers * 0.25)  # Very high engagement

        return linkedin_modified, instagram_modified

    def generate_complete_dataset(self, num_users: int = 1000, output_dir: str = "realistic_cross_platform_dataset"):
        """Generate a complete realistic cross-platform dataset."""

        print("🚀 Starting realistic cross-platform dataset generation...")
        print(f"👥 Generating {num_users} user personas...")

        # Step 1: Generate user personas
        personas = self.generate_realistic_user_personas(num_users)
        print(f"✅ Generated {len(personas)} realistic user personas")

        # Step 2: Generate LinkedIn profiles
        print("💼 Generating LinkedIn profiles...")
        linkedin_df = self.generate_linkedin_profiles(personas, match_ratio=0.8)
        print(f"✅ Generated {len(linkedin_df)} LinkedIn profiles")

        # Step 3: Generate Instagram profiles
        print("📱 Generating Instagram profiles...")
        instagram_df = self.generate_instagram_profiles(personas, linkedin_df, cross_platform_ratio=0.7)
        print(f"✅ Generated {len(instagram_df)} Instagram profiles")

        # Step 3.5: Add challenging edge cases
        print("🎯 Adding challenging edge cases and variations...")
        linkedin_df, instagram_df = self.add_challenging_edge_cases(linkedin_df, instagram_df, personas)
        print("✅ Added realistic edge cases and variations")

        # Step 4: Generate posts
        print("📝 Generating LinkedIn posts...")
        linkedin_posts = self.generate_linkedin_posts(linkedin_df, personas)
        print(f"✅ Generated {len(linkedin_posts)} LinkedIn posts")

        print("📸 Generating Instagram posts...")
        instagram_posts = self.generate_instagram_posts(instagram_df, personas)
        print(f"✅ Generated {len(instagram_posts)} Instagram posts")

        # Step 5: Generate networks
        print("🌐 Generating social networks...")
        linkedin_network, instagram_network = self.generate_networks(linkedin_df, instagram_df, personas)
        print(f"✅ Generated networks - LinkedIn: {linkedin_network.number_of_edges()} connections, Instagram: {instagram_network.number_of_edges()} connections")

        # Step 6: Generate ground truth
        print("🎯 Generating ground truth mappings...")
        ground_truth = self.generate_ground_truth(linkedin_df, instagram_df)
        print(f"✅ Generated {len(ground_truth)} ground truth mappings")

        # Step 7: Save all datasets
        print("💾 Saving datasets...")
        self.save_datasets(output_dir, linkedin_df, instagram_df, linkedin_posts,
                          instagram_posts, linkedin_network, instagram_network, ground_truth)

        print("🎉 Dataset generation completed successfully!")

        return {
            "linkedin_profiles": linkedin_df,
            "instagram_profiles": instagram_df,
            "linkedin_posts": linkedin_posts,
            "instagram_posts": instagram_posts,
            "linkedin_network": linkedin_network,
            "instagram_network": instagram_network,
            "ground_truth": ground_truth,
            "personas": personas
        }


def main():
    """Main function to generate the realistic dataset."""

    # Configuration
    NUM_USERS = 1000  # Adjust based on your needs
    OUTPUT_DIR = "realistic_cross_platform_dataset"

    print("=" * 60)
    print("🎯 REALISTIC CROSS-PLATFORM USER IDENTIFICATION DATASET")
    print("=" * 60)

    # Initialize generator
    generator = RealisticDatasetGenerator()

    # Generate complete dataset
    dataset = generator.generate_complete_dataset(
        num_users=NUM_USERS,
        output_dir=OUTPUT_DIR
    )

    print("\n" + "=" * 60)
    print("📊 DATASET SUMMARY")
    print("=" * 60)
    print(f"👥 Total unique personas: {NUM_USERS}")
    print(f"💼 LinkedIn profiles: {len(dataset['linkedin_profiles'])}")
    print(f"📱 Instagram profiles: {len(dataset['instagram_profiles'])}")
    print(f"🎯 Cross-platform matches: {len(dataset['ground_truth'][dataset['ground_truth']['match_confidence'] > 0])}")
    print(f"📝 Total posts: {len(dataset['linkedin_posts']) + len(dataset['instagram_posts'])}")
    print(f"🌐 Network connections: {dataset['linkedin_network'].number_of_edges() + dataset['instagram_network'].number_of_edges()}")

    # Display difficulty distribution
    difficulty_dist = dataset['ground_truth']['difficulty_level'].value_counts()
    print(f"\n🎯 Match Difficulty Distribution:")
    for level, count in difficulty_dist.items():
        print(f"   {level}: {count}")

    print(f"\n📁 Dataset saved to: {OUTPUT_DIR}")
    print("🚀 Ready for cross-platform user identification testing!")


if __name__ == "__main__":
    main()
