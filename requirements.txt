# Core dependencies with compatible versions
numpy>=1.21.0,<1.25.0
pandas>=1.3.0,<2.0.0
networkx>=2.6.0,<3.0.0
scikit-learn>=1.0.0,<1.4.0

# Deep learning and NLP
torch>=1.9.0,<2.1.0
transformers>=4.10.0,<5.0.0
sentence-transformers>=2.0.0,<3.0.0

# Visualization
matplotlib>=3.4.0,<4.0.0
seaborn>=0.11.0,<1.0.0
plotly>=5.0.0,<6.0.0

# Web framework
streamlit>=1.0.0,<2.0.0

# Web scraping
selenium>=4.0.0,<5.0.0
webdriver-manager>=3.5.0,<4.0.0
requests>=2.25.0,<3.0.0
beautifulsoup4>=4.9.0,<5.0.0
lxml>=4.6.0,<5.0.0

# NLP and text processing
nltk>=3.6.0,<4.0.0

# Configuration and utilities
pyyaml>=6.0,<7.0.0
tqdm>=4.62.0,<5.0.0
faker>=8.0.0,<20.0.0
joblib>=1.1.0,<2.0.0
pytz>=2021.1

# Graph embeddings - compatible versions
gensim>=4.1.0,<4.4.0
node2vec>=0.4.0,<1.0.0
karateclub>=1.0.0,<2.0.0

# Privacy and security
cryptography>=3.4.0,<42.0.0

# Machine learning extensions
lightgbm>=3.2.0,<4.0.0
xgboost>=1.4.0,<2.0.0

# Additional utilities
mlflow>=1.20.0,<3.0.0
optuna>=2.10.0,<4.0.0
