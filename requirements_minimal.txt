# Minimal requirements for Cross-Platform User Identification
# Core dependencies only to reduce disk space usage

# Data processing
numpy>=1.20.0
pandas>=1.3.0

# Machine learning
scikit-learn>=1.0.0
torch>=1.9.0,<2.0.0

# NLP and embeddings
transformers>=4.10.0
sentence-transformers>=2.0.0

# Graph processing
networkx>=2.6.0

# Visualization
matplotlib>=3.4.0
plotly>=5.0.0

# Web interface
streamlit>=1.0.0

# Utilities
pyyaml>=6.0
tqdm>=4.62.0
requests>=2.25.0

# Optional for development
nltk>=3.6.0
joblib>=1.1.0