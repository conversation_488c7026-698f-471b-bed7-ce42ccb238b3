ENHANCED CROSS-PLATFORM USER IDENTIFICATION USING MULTI-MODAL EMBEDDINGS AND ENSEMBLE LEARNING

Authors: <AUTHORS>
Department of Computer Science, Amrita Vishwa Vidyapeetham
<EMAIL>

Priti Gupta  
Department of Computer Science, Amrita Vishwa Vidyapeetham
<EMAIL>

ABSTRACT

Cross-platform user identification has become increasingly important for understanding user behavior across social media platforms. This paper presents an enhanced approach for identifying users across LinkedIn and Instagram using multi-modal embeddings and ensemble learning techniques. Our methodology combines semantic, network, temporal, and profile embeddings through advanced fusion mechanisms, followed by an ensemble of specialized matchers including Enhanced GSMUA, Advanced FRUI-P, and gradient boosting methods. Experimental results on a dataset of 147 LinkedIn and 98 Instagram users demonstrate superior performance with 87% F1-score, 89% precision, and 85% recall, significantly outperforming existing baseline methods.

Keywords: Cross-platform user identification, multi-modal embeddings, ensemble learning, social network analysis, feature fusion

1. INTRODUCTION

The proliferation of social media platforms has led to users maintaining multiple accounts across different services, creating a significant challenge for understanding comprehensive user behavior patterns. Cross-platform user identification has emerged as a critical research area with applications in recommendation systems, fraud detection, and social network analysis.

This paper addresses the limitations of existing methods by proposing an enhanced cross-platform user identification system that combines multi-modal feature extraction, advanced fusion techniques, ensemble learning, and comprehensive evaluation on real-world datasets.

2. METHODOLOGY

2.1 System Architecture
Our system consists of four main components: Multi-Modal Feature Extraction, Advanced Fusion, Ensemble Matching, and Final Prediction using meta-learning.

2.2 Multi-Modal Feature Extraction
- Semantic Embeddings: TF-IDF and BERT-based models
- Network Embeddings: GraphSAGE and Graph Convolutional Networks
- Temporal Embeddings: Time2Vec with Transformer architectures
- Profile Embeddings: Learned embeddings for demographic patterns

2.3 Ensemble Learning
Four specialized matchers:
- Enhanced GSMUA: Graph-based alignment with multi-head attention
- Advanced FRUI-P: Feature-rich identification with weighted propagation
- LightGBM: Gradient boosting for non-linear interactions
- Cosine Similarity: Optimized baseline with learned thresholds

3. EXPERIMENTAL RESULTS

3.1 Dataset
- 147 LinkedIn user profiles
- 98 Instagram user profiles  
- 156 ground truth pairs (81 matches, 75 non-matches)

3.2 Performance
- Precision: 89%
- Recall: 85%
- F1-Score: 87% (11.5% improvement over best baseline)
- AUC-ROC: 92%

4. CONCLUSION

This paper presented an enhanced approach for cross-platform user identification using multi-modal embeddings and ensemble learning. Experimental results demonstrate superior performance with significant improvements over existing approaches. Future work will explore federated learning and additional social media platforms.

REFERENCES

[1] Y. Zhang et al., "Cross-platform identification of anonymous identical users in multiple social media networks," IEEE Trans. Knowledge Data Eng., vol. 28, no. 2, pp. 411-424, 2015.

[2] S. Liu et al., "HYDRA: large-scale social identity linkage via heterogeneous behavior modeling," Proc. ACM SIGMOD, pp. 51-62, 2016.

[3] A. Vaswani et al., "Attention is all you need," Proc. NIPS, pp. 5998-6008, 2017.

[4] W. Hamilton et al., "Inductive representation learning on large graphs," Proc. NIPS, pp. 1024-1034, 2017.

[5] J. Devlin et al., "BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding," Proc. NAACL, pp. 4171-4186, 2019.