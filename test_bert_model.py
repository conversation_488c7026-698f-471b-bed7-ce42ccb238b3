#!/usr/bin/env python3
"""
Test script to verify BERT model is actually being used in the system.
"""

import os
import sys
import pandas as pd
import numpy as np
import time
from datetime import datetime

# Add src to path
sys.path.append('src')

def test_bert_model():
    """Test if BERT model is actually being used."""
    print("🔬 Testing BERT Model Implementation")
    print("=" * 50)
    
    try:
        # Import the semantic embedder
        from features.semantic_embedder import SemanticEmbedder
        print("✅ Successfully imported SemanticEmbedder")
        
        # Check what model is being used
        print("\n📊 Model Configuration:")
        print("Default model: sentence-transformers/all-MiniLM-L6-v2")
        print("Use SentenceTransformer: True")
        
        # Initialize the embedder
        print("\n🚀 Initializing SemanticEmbedder...")
        embedder = SemanticEmbedder(
            model_name='sentence-transformers/all-MiniLM-L6-v2',
            use_sentence_transformer=True
        )
        print(f"✅ Model loaded on device: {embedder.device}")
        print(f"✅ Model name: {embedder.model_name}")
        print(f"✅ Using SentenceTransformer: {embedder.use_sentence_transformer}")
        
        # Test with sample data
        print("\n🧪 Testing with sample social media data...")
        
        # Create sample data similar to the SNS dataset
        sample_data = pd.DataFrame({
            'user_id': ['user_1', 'user_2', 'user_3', 'user_1', 'user_2'],
            'content': [
                "I love machine learning and artificial intelligence research",
                "Traveling around the world and taking beautiful photographs",
                "Software engineer passionate about deep learning and neural networks",
                "Working on exciting AI projects and computer vision applications",
                "Photography enthusiast exploring new destinations and cultures"
            ]
        })
        
        print(f"Sample data: {len(sample_data)} posts from {sample_data['user_id'].nunique()} users")
        
        # Generate embeddings
        start_time = time.time()
        user_embeddings = embedder.fit_transform(
            data=sample_data,
            platform_name='test_platform',
            text_col='content',
            user_id_col='user_id',
            batch_size=2
        )
        end_time = time.time()
        
        print(f"✅ Generated embeddings in {end_time - start_time:.2f} seconds")
        print(f"✅ Number of users with embeddings: {len(user_embeddings)}")
        
        # Check embedding dimensions
        for user_id, embedding in user_embeddings.items():
            print(f"User {user_id}: embedding shape {embedding.shape}")
            break  # Just show one example
        
        # Verify embeddings are meaningful
        if len(user_embeddings) >= 2:
            users = list(user_embeddings.keys())
            emb1 = user_embeddings[users[0]]
            emb2 = user_embeddings[users[1]]
            
            # Calculate cosine similarity
            similarity = np.dot(emb1, emb2) / (np.linalg.norm(emb1) * np.linalg.norm(emb2))
            print(f"✅ Cosine similarity between {users[0]} and {users[1]}: {similarity:.3f}")
        
        # Test the underlying model directly
        print("\n🔍 Testing underlying SentenceTransformer model...")
        test_sentences = [
            "I am a machine learning researcher",
            "I work in artificial intelligence",
            "I love photography and travel"
        ]
        
        direct_embeddings = embedder.sentence_transformer.encode(test_sentences)
        print(f"✅ Direct model embeddings shape: {direct_embeddings.shape}")
        print(f"✅ Embedding dimension: {direct_embeddings.shape[1]}")
        
        # Verify this is actually a BERT-based model
        model_info = embedder.sentence_transformer._modules
        print(f"\n🏗️ Model Architecture:")
        for name, module in model_info.items():
            print(f"  {name}: {type(module).__name__}")
        
        return True, user_embeddings, direct_embeddings
        
    except Exception as e:
        print(f"❌ Error testing BERT model: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None, None

def test_with_real_dataset():
    """Test with actual SNS dataset."""
    print("\n🗂️ Testing with Real SNS Dataset")
    print("=" * 50)
    
    try:
        # Load a small sample from the real dataset
        linkedin_profiles = pd.read_csv("test_dataset/merged_linkedin_profiles.csv")
        
        # Take first 5 users for testing
        sample_profiles = linkedin_profiles.head(5)
        
        # Create test data with bio information
        test_data = []
        for _, profile in sample_profiles.iterrows():
            if pd.notna(profile.get('bio', '')):
                test_data.append({
                    'user_id': profile['user_id'],
                    'content': profile['bio']
                })
        
        if not test_data:
            print("⚠️ No bio data found in sample profiles")
            return False
        
        test_df = pd.DataFrame(test_data)
        print(f"Testing with {len(test_df)} real user profiles")
        
        # Initialize embedder
        from features.semantic_embedder import SemanticEmbedder
        embedder = SemanticEmbedder()
        
        # Generate embeddings
        start_time = time.time()
        real_embeddings = embedder.fit_transform(
            data=test_df,
            platform_name='linkedin_real',
            text_col='content',
            user_id_col='user_id'
        )
        end_time = time.time()
        
        print(f"✅ Generated embeddings for {len(real_embeddings)} real users")
        print(f"✅ Processing time: {end_time - start_time:.2f} seconds")
        
        # Show sample embedding
        if real_embeddings:
            sample_user = list(real_embeddings.keys())[0]
            sample_embedding = real_embeddings[sample_user]
            print(f"✅ Sample embedding shape: {sample_embedding.shape}")
            print(f"✅ Sample embedding stats: mean={sample_embedding.mean():.3f}, std={sample_embedding.std():.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing with real dataset: {str(e)}")
        return False

def main():
    """Main test function."""
    print("🧪 BERT Model Verification Test")
    print("=" * 60)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test 1: Basic BERT model functionality
    success1, user_embeddings, direct_embeddings = test_bert_model()
    
    # Test 2: Real dataset
    success2 = test_with_real_dataset()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Basic BERT Model Test: {'PASSED' if success1 else 'FAILED'}")
    print(f"✅ Real Dataset Test: {'PASSED' if success2 else 'FAILED'}")
    
    if success1:
        print("\n🎯 BERT MODEL VERIFICATION RESULTS:")
        print("✅ BERT model is properly implemented and working")
        print("✅ SentenceTransformer (BERT-based) is being used")
        print("✅ Model: sentence-transformers/all-MiniLM-L6-v2")
        print("✅ Embeddings are generated correctly")
        print("✅ Semantic similarity calculations work")
        
        if user_embeddings:
            sample_embedding = list(user_embeddings.values())[0]
            print(f"✅ Embedding dimension: {sample_embedding.shape[0]}")
            print(f"✅ Expected dimension (paper): 768 (this model: {sample_embedding.shape[0]})")
            
            if sample_embedding.shape[0] != 768:
                print("⚠️ NOTE: Using all-MiniLM-L6-v2 (384D) instead of BERT-base (768D)")
                print("   This is still a BERT-based model, just smaller/faster")
    else:
        print("\n❌ BERT MODEL ISSUES DETECTED:")
        print("❌ BERT model is not working properly")
        print("❌ Check dependencies and model installation")
    
    print("\n" + "=" * 60)
    
    return success1 and success2

if __name__ == "__main__":
    main()
