"""
Test the real CrossEmbedUID implementation in the app
"""

import sys
sys.path.append('src')

import pandas as pd
import numpy as np
from app import RealCrossEmbedUIDSystem

def test_real_system():
    """Test the real CrossEmbedUID system."""
    print("🔧 Testing Real CrossEmbedUID System")
    
    # Create sample data
    linkedin_data = pd.DataFrame({
        'user_id': ['ln_1', 'ln_2', 'ln_3'],
        'name': ['<PERSON>', '<PERSON>', '<PERSON>'],
        'bio': ['Software engineer at tech company', 'Data scientist and ML enthusiast', 'Product manager with 5 years experience'],
        'location': ['San Francisco', 'New York', 'Seattle'],
        'field_of_interest': ['Technology', 'Machine Learning', 'Product Management']
    })
    
    instagram_data = pd.DataFrame({
        'user_id': ['ig_1', 'ig_2', 'ig_3'],
        'name': ['<PERSON>', '<PERSON>', '<PERSON>'],
        'bio': ['Tech lover and coder', 'AI researcher and data geek', 'PM building great products'],
        'location': ['SF Bay Area', 'NYC', 'Seattle WA'],
        'field_of_interest': ['Programming', 'Artificial Intelligence', 'Product Development']
    })
    
    print(f"📊 LinkedIn data: {len(linkedin_data)} users")
    print(f"📊 Instagram data: {len(instagram_data)} users")
    
    # Initialize system
    system = RealCrossEmbedUIDSystem()
    
    # Test component initialization
    print("🔧 Initializing components...")
    success = system.initialize_components()
    print(f"✅ Components initialized: {success}")
    
    # Test semantic feature extraction
    print("🧠 Testing semantic feature extraction...")
    
    def progress_callback(message):
        print(f"  {message}")
    
    linkedin_semantic = system.extract_semantic_features(linkedin_data, 'linkedin', progress_callback)
    instagram_semantic = system.extract_semantic_features(instagram_data, 'instagram', progress_callback)
    
    print(f"✅ LinkedIn semantic features: {len(linkedin_semantic)} users")
    print(f"✅ Instagram semantic features: {len(instagram_semantic)} users")
    
    # Test profile feature extraction
    print("👤 Testing profile feature extraction...")
    linkedin_profile = system.extract_profile_features(linkedin_data, 'linkedin', progress_callback)
    instagram_profile = system.extract_profile_features(instagram_data, 'instagram', progress_callback)
    
    print(f"✅ LinkedIn profile features: {len(linkedin_profile)} users")
    print(f"✅ Instagram profile features: {len(instagram_profile)} users")
    
    # Test feature fusion
    print("🔗 Testing feature fusion...")
    linkedin_fused = system.fuse_features(linkedin_semantic, {}, linkedin_profile, 'linkedin', progress_callback)
    instagram_fused = system.fuse_features(instagram_semantic, {}, instagram_profile, 'instagram', progress_callback)
    
    print(f"✅ LinkedIn fused features: {len(linkedin_fused)} users")
    print(f"✅ Instagram fused features: {len(instagram_fused)} users")
    
    # Test user matching
    print("🎯 Testing user matching...")
    matches = system.match_users(linkedin_fused, instagram_fused, progress_callback)
    
    print(f"✅ Found {len(matches)} matches")
    
    # Display matches
    if matches:
        print("\n📋 Match Results:")
        for i, match in enumerate(matches[:5]):  # Show first 5 matches
            print(f"  {i+1}. {match['linkedin_user_id']} ↔ {match['instagram_user_id']} (confidence: {match['confidence']:.3f})")
    
    # Test full analysis
    print("\n🚀 Testing full analysis pipeline...")
    results = system.run_full_analysis(linkedin_data, instagram_data, progress_callback=progress_callback)
    
    if results:
        print(f"✅ Full analysis complete!")
        print(f"  - Matches found: {len(results['matches'])}")
        print(f"  - LinkedIn features: {results['linkedin_features']}")
        print(f"  - Instagram features: {results['instagram_features']}")
        print(f"  - Semantic dimension: {results['semantic_dim']}")
    else:
        print("❌ Full analysis failed")
    
    print("\n🎉 Real CrossEmbedUID system test complete!")
    return results

if __name__ == "__main__":
    test_real_system()
