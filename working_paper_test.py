#!/usr/bin/env python3
"""
Working test of the actual CrossEmbedUID implementation.
Tests individual components and measures real performance.
"""

import os
import sys
import pandas as pd
import numpy as np
import time
import json
from datetime import datetime
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')

def test_semantic_embedder():
    """Test the actual BERT-based semantic embedder."""
    print("🧠 Testing Semantic Embedder (BERT)")
    print("-" * 40)
    
    try:
        from features.semantic_embedder import SemanticEmbedder
        
        # Initialize with paper-specified model
        print("Initializing BERT model...")
        embedder = SemanticEmbedder(
            model_name='sentence-transformers/all-mpnet-base-v2',  # Better BERT model
            use_sentence_transformer=True
        )
        
        print(f"✅ Model loaded: {embedder.model_name}")
        print(f"✅ Device: {embedder.device}")
        
        # Load sample data
        linkedin_profiles = pd.read_csv("test_dataset/merged_linkedin_profiles.csv").head(10)
        
        # Create test posts from profiles
        test_data = []
        for _, profile in linkedin_profiles.iterrows():
            # Use field_of_interest as content
            content = f"{profile['name']} works as {profile['field_of_interest']} in {profile['location']}"
            test_data.append({
                'user_id': profile['user_id'],
                'content': content
            })
        
        test_df = pd.DataFrame(test_data)
        print(f"Testing with {len(test_df)} user profiles")
        
        # Generate embeddings
        start_time = time.time()
        embeddings = embedder.fit_transform(
            data=test_df,
            platform_name='linkedin',
            text_col='content',
            user_id_col='user_id',
            batch_size=4
        )
        end_time = time.time()
        
        print(f"✅ Generated embeddings for {len(embeddings)} users")
        print(f"✅ Processing time: {end_time - start_time:.2f} seconds")
        
        # Check embedding properties
        if embeddings:
            sample_embedding = list(embeddings.values())[0]
            print(f"✅ Embedding dimension: {sample_embedding.shape[0]}")
            print(f"✅ Embedding stats: mean={sample_embedding.mean():.3f}, std={sample_embedding.std():.3f}")
            
            # Test similarity
            if len(embeddings) >= 2:
                users = list(embeddings.keys())
                emb1 = embeddings[users[0]]
                emb2 = embeddings[users[1]]
                similarity = np.dot(emb1, emb2) / (np.linalg.norm(emb1) * np.linalg.norm(emb2))
                print(f"✅ Sample similarity: {similarity:.3f}")
        
        return True, embeddings
        
    except Exception as e:
        print(f"❌ Semantic embedder test failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_network_embedder():
    """Test the network embedder."""
    print("\n🕸️ Testing Network Embedder")
    print("-" * 40)
    
    try:
        from features.network_embedder import NetworkEmbedder
        
        # Create sample network data
        users = [f"user_{i}" for i in range(20)]
        edges = []
        np.random.seed(42)
        for i in range(30):  # Create 30 random edges
            source = np.random.choice(users)
            target = np.random.choice(users)
            if source != target:
                edges.append({'source': source, 'target': target})
        
        network_df = pd.DataFrame(edges)
        print(f"Testing with {len(network_df)} edges, {len(users)} users")
        
        # Initialize embedder
        embedder = NetworkEmbedder()
        
        # Generate embeddings
        start_time = time.time()
        embeddings = embedder.fit_transform(
            network_data=network_df,
            platform_name='test_network'
        )
        end_time = time.time()
        
        print(f"✅ Generated network embeddings for {len(embeddings)} users")
        print(f"✅ Processing time: {end_time - start_time:.2f} seconds")
        
        if embeddings:
            sample_embedding = list(embeddings.values())[0]
            print(f"✅ Network embedding dimension: {sample_embedding.shape[0]}")
        
        return True, embeddings
        
    except Exception as e:
        print(f"❌ Network embedder test failed: {e}")
        return False, None

def test_user_matching():
    """Test the user matching component."""
    print("\n🎯 Testing User Matching")
    print("-" * 40)
    
    try:
        from models.user_matcher import UserMatcher
        
        # Create sample embeddings
        linkedin_embeddings = {}
        instagram_embeddings = {}
        
        np.random.seed(42)
        for i in range(10):
            # Create similar embeddings for same users
            base_embedding = np.random.normal(0, 1, 384)
            linkedin_embeddings[f"linkedin_user_{i}"] = base_embedding + np.random.normal(0, 0.1, 384)
            instagram_embeddings[f"instagram_user_{i}"] = base_embedding + np.random.normal(0, 0.1, 384)
        
        print(f"Testing with {len(linkedin_embeddings)} LinkedIn and {len(instagram_embeddings)} Instagram users")
        
        # Initialize matcher
        matcher = UserMatcher()
        
        # Perform matching
        start_time = time.time()
        matches = matcher.match_users(
            linkedin_embeddings,
            instagram_embeddings,
            'linkedin',
            'instagram'
        )
        end_time = time.time()
        
        print(f"✅ Generated {len(matches)} matches")
        print(f"✅ Matching time: {end_time - start_time:.2f} seconds")
        
        # Show sample matches
        if matches:
            for i, match in enumerate(matches[:3]):
                print(f"  Match {i+1}: {match}")
        
        return True, matches
        
    except Exception as e:
        print(f"❌ User matching test failed: {e}")
        return False, None

def test_end_to_end_pipeline():
    """Test the complete end-to-end pipeline."""
    print("\n🔄 Testing End-to-End Pipeline")
    print("-" * 40)
    
    try:
        # Load real dataset (limited for testing)
        linkedin_profiles = pd.read_csv("test_dataset/merged_linkedin_profiles.csv").head(20)
        instagram_profiles = pd.read_csv("test_dataset/merged_instagram_profiles.csv").head(20)
        ground_truth = pd.read_csv("test_dataset/merged_ground_truth.csv").head(50)
        
        print(f"Testing with {len(linkedin_profiles)} LinkedIn, {len(instagram_profiles)} Instagram users")
        
        # Step 1: Generate semantic embeddings
        print("\nStep 1: Semantic embeddings...")
        semantic_success, linkedin_semantic = test_semantic_embedder_for_platform(
            linkedin_profiles, 'linkedin'
        )
        
        if semantic_success:
            _, instagram_semantic = test_semantic_embedder_for_platform(
                instagram_profiles, 'instagram'
            )
        else:
            return False, None
        
        # Step 2: Simple matching
        print("\nStep 2: Performing matching...")
        matches = perform_simple_matching(linkedin_semantic, instagram_semantic)
        
        # Step 3: Evaluation
        print("\nStep 3: Evaluation...")
        results = evaluate_matches(matches, ground_truth)
        
        return True, results
        
    except Exception as e:
        print(f"❌ End-to-end test failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_semantic_embedder_for_platform(profiles, platform_name):
    """Helper function to test semantic embedder for a platform."""
    try:
        from features.semantic_embedder import SemanticEmbedder
        
        embedder = SemanticEmbedder(
            model_name='sentence-transformers/all-MiniLM-L6-v2',
            use_sentence_transformer=True
        )
        
        # Create content from profiles
        test_data = []
        for _, profile in profiles.iterrows():
            content = f"{profile['name']} works as {profile['field_of_interest']} in {profile['location']}"
            test_data.append({
                'user_id': profile['user_id'],
                'content': content
            })
        
        test_df = pd.DataFrame(test_data)
        
        embeddings = embedder.fit_transform(
            data=test_df,
            platform_name=platform_name,
            text_col='content',
            user_id_col='user_id',
            batch_size=8
        )
        
        print(f"  ✅ {platform_name}: {len(embeddings)} embeddings")
        return True, embeddings
        
    except Exception as e:
        print(f"  ❌ {platform_name} embeddings failed: {e}")
        return False, None

def perform_simple_matching(linkedin_embeddings, instagram_embeddings):
    """Perform simple cosine similarity matching."""
    from sklearn.metrics.pairwise import cosine_similarity
    
    # Convert to matrices
    linkedin_users = list(linkedin_embeddings.keys())
    instagram_users = list(instagram_embeddings.keys())
    
    linkedin_matrix = np.array([linkedin_embeddings[user] for user in linkedin_users])
    instagram_matrix = np.array([instagram_embeddings[user] for user in instagram_users])
    
    # Calculate similarity matrix
    similarity_matrix = cosine_similarity(linkedin_matrix, instagram_matrix)
    
    # Generate matches
    matches = []
    threshold = 0.3
    
    for i, linkedin_user in enumerate(linkedin_users):
        for j, instagram_user in enumerate(instagram_users):
            similarity = similarity_matrix[i, j]
            if similarity > threshold:
                matches.append({
                    'linkedin_user_id': linkedin_user,
                    'instagram_user_id': instagram_user,
                    'similarity': similarity,
                    'confidence': similarity
                })
    
    # Sort by similarity
    matches.sort(key=lambda x: x['similarity'], reverse=True)
    
    print(f"  ✅ Generated {len(matches)} matches above threshold {threshold}")
    return matches

def evaluate_matches(matches, ground_truth):
    """Evaluate matches against ground truth."""
    # Create ground truth lookup
    gt_pairs = set(zip(ground_truth['linkedin_id'], ground_truth['instagram_id']))
    
    # Evaluate matches
    correct_matches = 0
    total_matches = len(matches)
    
    for match in matches:
        linkedin_id = match['linkedin_user_id']
        instagram_id = match['instagram_user_id']
        
        if (linkedin_id, instagram_id) in gt_pairs:
            correct_matches += 1
    
    # Calculate metrics
    precision = correct_matches / total_matches if total_matches > 0 else 0
    recall = correct_matches / len(gt_pairs) if len(gt_pairs) > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    results = {
        'total_matches': total_matches,
        'correct_matches': correct_matches,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'ground_truth_pairs': len(gt_pairs)
    }
    
    print(f"  ✅ Precision: {precision:.3f}")
    print(f"  ✅ Recall: {recall:.3f}")
    print(f"  ✅ F1-Score: {f1:.3f}")
    
    return results

def main():
    """Main test function."""
    print("🔬 CROSSEMBEDUID REAL IMPLEMENTATION TEST")
    print("=" * 60)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    results = {}
    
    # Test individual components
    semantic_success, semantic_embeddings = test_semantic_embedder()
    results['semantic_test'] = semantic_success
    
    network_success, network_embeddings = test_network_embedder()
    results['network_test'] = network_success
    
    matching_success, matches = test_user_matching()
    results['matching_test'] = matching_success
    
    # Test end-to-end pipeline
    e2e_success, e2e_results = test_end_to_end_pipeline()
    results['end_to_end_test'] = e2e_success
    results['e2e_results'] = e2e_results
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Semantic Embedder: {'PASSED' if semantic_success else 'FAILED'}")
    print(f"✅ Network Embedder: {'PASSED' if network_success else 'FAILED'}")
    print(f"✅ User Matching: {'PASSED' if matching_success else 'FAILED'}")
    print(f"✅ End-to-End Pipeline: {'PASSED' if e2e_success else 'FAILED'}")
    
    if e2e_results:
        print(f"\n🎯 REAL PERFORMANCE RESULTS:")
        print(f"Precision: {e2e_results['precision']:.3f}")
        print(f"Recall: {e2e_results['recall']:.3f}")
        print(f"F1-Score: {e2e_results['f1_score']:.3f}")
        print(f"Total Matches: {e2e_results['total_matches']}")
        print(f"Correct Matches: {e2e_results['correct_matches']}")
    
    # Save results
    os.makedirs("real_test_results", exist_ok=True)
    with open("real_test_results/implementation_test.json", 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📁 Results saved to real_test_results/")
    print("=" * 60)
    
    return all([semantic_success, network_success, matching_success, e2e_success])

if __name__ == "__main__":
    main()
